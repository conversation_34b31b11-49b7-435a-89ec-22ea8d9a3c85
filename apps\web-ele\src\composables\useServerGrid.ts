import type { ColDef, RowSelectionOptions } from 'ag-grid-community';
import type { ActionItem } from '#/components/table-action';
import { ref, reactive, computed } from 'vue';
import { useMessage } from '#/components/elementPlus/useMessage';

// 服务端数据源接口
interface ServerDataSource {
  getRows: (params: any) => Promise<{
    data: any[];
    total: number;
  }>;
}

// 配置选项接口
interface UseServerGridOptions {
  // API 调用函数
  apiFunction: (params: any) => Promise<any>;
  // 列定义
  columnDefs: ColDef[];
  // 默认页面大小
  defaultPageSize?: number;
  // 行选择配置
  rowSelection?: RowSelectionOptions;
  // 表格顶部操作按钮
  tableActions?: ActionItem[];
  // 默认列配置
  defaultColDef?: any;
  // 搜索参数处理函数
  searchParamsProcessor?: (params: any) => any;
  // 响应数据处理函数
  responseProcessor?: (response: any) => { data: any[]; total: number };
}

// 默认响应处理函数
const defaultResponseProcessor = (response: any) => {
  let responseData, responseTotal;

  if (response && (response.code === 0 || response.Code === 0)) {
    responseData = response.data || response.Data || [];
    responseTotal = response.totalCount || response.TotalCount || 0;
  } else if (Array.isArray(response)) {
    responseData = response;
    responseTotal = response.length;
  } else {
    responseData = [];
    responseTotal = 0;
  }

  return {
    data: responseData,
    total: responseTotal,
  };
};

export function useServerGrid(options: UseServerGridOptions) {
  const {
    apiFunction,
    columnDefs,
    defaultPageSize = 20,
    rowSelection = {
      mode: 'singleRow',
      checkboxes: false,
      headerCheckbox: false,
      enableClickSelection: false,
      copySelectedRows: false,
    },
    tableActions = [],
    defaultColDef = {},
    searchParamsProcessor,
    responseProcessor = defaultResponseProcessor,
  } = options;

  // 响应式状态
  const gridRef = ref();
  const loading = ref(false);
  const currentPageSize = ref(defaultPageSize);
  const searchParams = ref<any>({});
  const isRefreshing = ref(false);

  // 服务端数据源
  const dataSource: ServerDataSource = {
    getRows: async (params: any) => {
      try {
        loading.value = true;
        
        const { startRow, pageIndex, pageSize } = params;
        const actualPageSize = pageSize || currentPageSize.value;

        // 构建查询参数
        const queryParams: any = {
          pageIndex: pageIndex || Math.floor((startRow || 0) / actualPageSize) + 1,
          pageSize: actualPageSize,
        };

        // 添加搜索参数
        if (searchParams.value) {
          const processedSearchParams = searchParamsProcessor 
            ? searchParamsProcessor(searchParams.value)
            : searchParams.value;
          Object.assign(queryParams, processedSearchParams);
        }

        // 调用 API
        const response = await apiFunction(queryParams);
        
        // 处理响应数据
        const result = responseProcessor(response);

        return result;
      } catch (error) {
        console.error('获取数据失败:', error);
        throw error;
      } finally {
        loading.value = false;
      }
    },
  };

  // 事件处理函数
  const handleDataLoaded = (result: { data: any[]; total: number }) => {
    // 数据加载完成，可以在这里处理一些逻辑
    console.log('数据加载完成:', result);
  };

  const handleLoadError = (error: any) => {
    console.error('数据加载失败:', error);
    useMessage().showMessage('error', '数据加载失败，请重试');
  };

  const handlePaginationChanged = (params: { pageIndex: number; pageSize: number }) => {
    console.log('分页变化:', params);
    currentPageSize.value = params.pageSize;
  };

  // 刷新表格
  const refresh = () => {
    if (gridRef.value && gridRef.value.handleRefresh) {
      gridRef.value.handleRefresh();
    }
  };

  // 搜索函数
  const search = (params: any) => {
    if (isRefreshing.value) {
      console.warn('正在刷新中，跳过此次请求');
      return;
    }

    isRefreshing.value = true;

    // 处理页面大小
    if (params.pageSize) {
      currentPageSize.value = Number(params.pageSize);
    }

    // 从搜索参数中移除 pageSize，避免传递给后端
    const { pageSize: _pageSize, ...searchData } = params;
    searchParams.value = searchData;

    // 刷新表格数据
    refresh();

    // 延迟重置标志
    setTimeout(() => {
      isRefreshing.value = false;
    }, 1000);
  };

  // 获取选中的行
  const getSelectedRows = () => {
    return gridRef.value?.gridApi?.getSelectedRows() || [];
  };

  // 获取当前页面大小
  const getCurrentPageSize = () => {
    if (gridRef.value && gridRef.value.getCurrentPageSize) {
      return gridRef.value.getCurrentPageSize();
    }
    return currentPageSize.value;
  };

  // 表格配置
  const gridConfig = computed(() => ({
    ref: gridRef,
    columnDefs,
    dataSource,
    pageSize: currentPageSize.value,
    defaultColDef,
    rowSelection,
    tableActions,
    loading: loading.value,
    onDataLoaded: handleDataLoaded,
    onLoadError: handleLoadError,
    onPaginationChanged: handlePaginationChanged,
  }));

  return {
    // 响应式状态
    gridRef,
    loading,
    currentPageSize,
    searchParams,
    
    // 表格配置
    gridConfig,
    
    // 方法
    search,
    refresh,
    getSelectedRows,
    getCurrentPageSize,
    
    // 事件处理函数（可以被覆盖）
    handleDataLoaded,
    handleLoadError,
    handlePaginationChanged,
  };
}
