# ServerGridComponent 简化使用指南

## 新增的简化 API

ServerGridComponent 现在支持更简单的调用方式，减少重复代码，让调用更加简洁。

## 简化的使用方式

### 1. 基本用法（推荐）

```vue
<template>
  <PageCard auto-content-height>
    <!-- 搜索表单 -->
    <QueryForm />
    
    <!-- 表格操作按钮 -->
    <TableAction :actions="tableActions" />
    
    <!-- 简化的表格组件 -->
    <ServerGridComponent
      ref="gridRef"
      :api-function="getMaterialList"
      :column-defs="columnDefs"
      :table-actions="[]"
      @data-loaded="handleDataLoaded"
      @load-error="handleLoadError"
    />
  </PageCard>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { getMaterialList } from '#/api/production/material';
import ServerGridComponent from '#/components/ag-grid/ServerGridComponent.vue';
import { useMessage } from '#/components/elementPlus/useMessage';
import { $t } from '#/locales';

const gridRef = ref();

// 列定义
const columnDefs = [
  { headerName: $t('production.RawMaterialCode'), field: 'mCode', width: 135 },
  { headerName: $t('production.RawMaterialName'), field: 'mName', width: 140 },
  {
    headerName: $t('production.Action'),
    field: 'action',
    pinned: 'right',
    cellRenderer: 'actionCell',
    flex: 1,
    cellRendererParams: {
      actions: [
        {
          label: $t('production.Edit'),
          callback: (data: any) => handleEdit(data),
          auth: ['production.material.edit'],
          type: 'primary',
          size: 'small',
        },
        {
          label: $t('production.Delete'),
          callback: (data: any) => handleDelete(data),
          auth: ['production.material.delete'],
          type: 'danger',
          size: 'small',
        },
      ],
    },
  },
];

// 表格操作按钮
const tableActions = [
  {
    label: $t('production.Add'),
    type: 'primary',
    icon: 'ep:plus',
    auth: ['production.material.add'],
    onClick: handleAdd,
  },
];

// 搜索表单配置
const [QueryForm] = useVbenForm({
  handleSubmit: (values) => {
    // 直接调用表格的搜索方法
    gridRef.value?.search(values);
  },
  schema: [
    {
      label: $t('production.RawMaterialCode'),
      component: 'Input',
      fieldName: 'mCode',
    },
    {
      label: $t('production.RawMaterialName'),
      component: 'Input',
      fieldName: 'mName',
    },
    {
      label: '页面大小',
      component: 'Select',
      fieldName: 'pageSize',
      componentProps: {
        options: [
          { label: '10条/页', value: 10 },
          { label: '20条/页', value: 20 },
          { label: '50条/页', value: 50 },
          { label: '100条/页', value: 100 },
        ],
      },
    },
  ],
});

// 事件处理
const handleDataLoaded = (result: { data: any[]; total: number }) => {
  console.log('数据加载完成:', result);
};

const handleLoadError = (error: any) => {
  console.error('数据加载失败:', error);
  useMessage().showMessage('error', '数据加载失败，请重试');
};

const handleAdd = () => {
  // 添加逻辑
};

const handleEdit = (data: any) => {
  // 编辑逻辑
  // 编辑完成后刷新表格
  // gridRef.value?.handleRefresh();
};

const handleDelete = async (data: any) => {
  // 删除逻辑
  // 删除完成后刷新表格
  // gridRef.value?.handleRefresh();
};
</script>
```

### 2. 自定义响应数据处理

```vue
<template>
  <ServerGridComponent
    ref="gridRef"
    :api-function="getCustomList"
    :response-processor="customResponseProcessor"
    :search-params-processor="customSearchProcessor"
    :column-defs="columnDefs"
  />
</template>

<script lang="ts" setup>
// 自定义响应数据处理函数
const customResponseProcessor = (response: any) => {
  return {
    data: response.result?.items || [],
    total: response.result?.totalCount || 0,
  };
};

// 自定义搜索参数处理函数
const customSearchProcessor = (params: any) => {
  // 可以在这里转换参数格式
  return {
    ...params,
    // 添加额外的参数
    status: 'active',
  };
};
</script>
```

## 新增的 Props

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| apiFunction | Function | null | API 调用函数，接收查询参数，返回 Promise |
| responseProcessor | Function | null | 响应数据处理函数，用于转换 API 返回的数据格式 |
| searchParamsProcessor | Function | null | 搜索参数处理函数，用于转换搜索参数格式 |

## 新增的方法

通过 ref 可以访问以下新方法：

| 方法名 | 参数 | 说明 |
| --- | --- | --- |
| search | params | 搜索方法，接收搜索参数并刷新表格 |
| getSelectedRows | - | 获取选中的行数据 |

## 优势

1. **减少重复代码**：不需要手动编写 dataSource.getRows 方法
2. **内置数据处理**：自动处理常见的 API 响应格式
3. **简化搜索**：内置搜索参数管理和页面大小处理
4. **向后兼容**：仍然支持原有的 dataSource 方式

## 迁移指南

### 从旧方式迁移到新方式

**旧方式：**
```javascript
// 需要手动编写 dataSource
const dataSource = {
  getRows: async (params) => {
    const { pageIndex, pageSize } = params;
    const queryParams = { pageIndex, pageSize };
    
    if (searchParams.value) {
      Object.assign(queryParams, searchParams.value);
    }
    
    const res = await getMaterialList(queryParams);
    
    return {
      data: res.data || [],
      total: res.totalCount || 0,
    };
  },
};
```

**新方式：**
```javascript
// 直接传入 API 函数即可
:api-function="getMaterialList"
```

这样就可以大大简化代码，让组件调用更加简洁！
