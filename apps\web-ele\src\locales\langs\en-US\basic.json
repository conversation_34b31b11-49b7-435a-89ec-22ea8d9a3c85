﻿{
  "PleaseSelectData": "Please select data!",
  "UpdateSuccess": "Update successful",
  "add": "Add",
  "addSuccess": "Add successful",
  "basci": {
    "view": "View"
  },
  "baseic": {
    "close": "Close"
  },
  "cancel": "Cancel",
  "close": "Close",
  "confirm": "Confirm",
  "confirmDelete": "Are you sure you want to delete?",
  "confirmSave": "Are you sure you want to save?",
  "confrimCancel": "Are you sure you want to cancel?",
  "createTime": "Create Time",
  "createUserName": "Creator",
  "delete": "Delete",
  "deleteFailed": "Delete failed",
  "deleteSuccess": "Delete successful",
  "details": "Details",
  "downloadSuccess": "Download successful!",
  "edit": "Edit",
  "endDate": "End Date",
  "fetchFailed": "Request failed",
  "import": "Import",
  "operation": "Operation",
  "pleaseSelect": "Please select",
  "refresh": "Refresh",
  "reset": "Reset",
  "save": "Save",
  "saveFailed": "Save failed",
  "search": "Search",
  "selectInput": "Please enter search",
  "startDate": "Start Date",
  "submit": "Submit",
  "tips": "Tips",
  "updateSuccess": "Update successful",
  "view": "View"
}
