import type { Component } from 'vue';

/**
 * 可展开卡片的配置接口
 */
export interface ExpandableCardConfig {
  /** 卡片标题 */
  title: string;
  /** 图标组件 */
  icon?: Component;
  /** 图标样式类名 */
  iconClass?: string;
  /** 是否默认展开 */
  defaultExpanded?: boolean;
  /** 卡片内容组件 */
  content?: Component;
  /** 卡片内容的 props */
  contentProps?: Record<string, any>;
  /** 卡片样式类名 */
  cardClass?: string;
  /** 头部样式类名 */
  headerClass?: string;
  /** 内容样式类名 */
  contentClass?: string;
}

/**
 * 可展开卡片组件的 Props
 */
export interface ExpandableCardProps {
  /** 卡片配置 */
  config: ExpandableCardConfig;
  /** 是否禁用展开/收缩功能 */
  disabled?: boolean;
  /** 自定义展开状态（受控模式） */
  expanded?: boolean;
}

/**
 * 可展开卡片组件的 Emits
 */
export interface ExpandableCardEmits {
  /** 展开状态变化事件 */
  'update:expanded': [expanded: boolean];
  /** 卡片点击事件 */
  'card-click': [config: ExpandableCardConfig];
}
