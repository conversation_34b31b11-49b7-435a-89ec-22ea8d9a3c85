<script lang="ts" setup>
import type { ColDef } from 'ag-grid-community';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElNotification } from 'element-plus';

import { useVbenForm } from '#/adapter/form';
import {
  addOrEditImported,
  getImportedInfo,
  getPurchaseOrderDetailAll,
} from '#/api/purchase/imported';
import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';
import { $t } from '#/locales';

/**
 * 状态变量
 * isUpdate: 是否为更新模式
 * isView: 是否为查看模式
 * record: 当前记录数据
 */
const isUpdate = ref(false);
const isView = ref(false);
const record = ref<any>({});

/**
 * 明细相关变量
 */
const detailGridRef = ref();
const detailRowData = ref<any[]>([]);

/**
 * 明细表格列定义
 */
const detailColumnDefs: ColDef[] = [
  {
    headerName: $t('purchase.seq'),
    field: 'seq',
    width: 80,
  },
  {
    headerName: $t('purchase.mCode'),
    field: 'mCode',
    width: 120,
  },
  {
    headerName: $t('purchase.itemCode'),
    field: 'itemCode',
    width: 120,
  },
  {
    headerName: $t('purchase.itemName'),
    field: 'itemName',
    width: 150,
  },
  {
    headerName: $t('purchase.specModel'),
    field: 'specModel',
    width: 120,
  },
  {
    headerName: $t('purchase.unit'),
    field: 'unit',
    width: 80,
  },
  {
    headerName: $t('purchase.qty'),
    field: 'qty',
    width: 100,
  },
  {
    headerName: $t('purchase.packageCount'),
    field: 'packageCount',
    width: 100,
  },
  {
    headerName: $t('purchase.grossWeight'),
    field: 'grossWeight',
    width: 100,
  },
  {
    headerName: $t('purchase.netWeight'),
    field: 'netWeight',
    width: 100,
  },
  {
    headerName: $t('purchase.volume'),
    field: 'volume',
    width: 100,
  },
  {
    headerName: $t('purchase.unitPrice'),
    field: 'unitPrice',
    width: 100,
  },
  {
    headerName: $t('purchase.amount'),
    field: 'amount',
    width: 100,
  },
  {
    headerName: $t('purchase.currency'),
    field: 'currency',
    width: 80,
  },
];

/**
 * 明细表格默认列配置
 */
const detailDefaultColDef = {
  sortable: true,
  filter: true,
  resizable: true,
  flex: 1,
};

/**
 * 明细表格行选择配置
 */
const detailRowSelection = {
  mode: 'singleRow' as const,
  checkboxes: false,
  headerCheckbox: false,
  enableClickSelection: true,
};

// 获取明细数据
const getDetailData = async () => {
  if (!record.value.id) return;

  try {
    const res = await getPurchaseOrderDetailAll({
      orderNo: record.value.orderNo,
    });
    if (Array.isArray(res)) {
      detailRowData.value = res;
      setTimeout(() => {
        if (detailGridRef.value?.gridApi) {
          detailGridRef.value.gridApi.autoSizeAllColumns();
        }
      }, 10);
    }
  } catch (error) {
    console.error('获取明细数据失败:', error);
    ElNotification({
      duration: 2500,
      message: '获取明细数据失败',
      type: 'error',
    });
  }
};

/**
 * 计算弹窗标题
 * 根据当前模式（新增/编辑/查看）返回对应的标题
 */
const title = computed(() => {
  if (isView.value) {
    return $t('purchase.viewLocal'); // 查看国内采购
  }
  return isUpdate.value ? $t('purchase.editLocal') : $t('purchase.addLocal'); // 编辑国外进口 : 添加国外进口
});

/**
 * 创建表单组件
 * 定义表单字段、验证规则和提交处理函数
 */
const [AddForm, formApi] = useVbenForm({
  // 显示默认的提交和重置按钮
  showDefaultActions: false,
  // 所有表单项的通用配置
  commonConfig: {
    componentProps: {
      class: 'w-full',
      readonly: isView, // 查看模式下表单只读
    },
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4',
  // 表单字段定义
  schema: [
    {
      fieldName: 'orderNo',
      component: 'Input',
      label: $t('purchase.importOrderNo'),
      componentProps: {
        readonly: true, // 查看模式下表单只读
      },
    },
    {
      fieldName: 'orderStatus',
      component: 'Input',
      label: $t('purchase.orderStatus'),
      componentProps: {
        readonly: true, // 查看模式下表单只读
      },
    },
    {
      fieldName: 'poNo',
      component: 'Input',
      label: $t('purchase.poNo'),
    },
    {
      fieldName: 'declarationNo',
      component: 'Input',
      label: $t('purchase.declarationNo'),
    },
    {
      fieldName: 'supplier',
      component: 'Input',
      label: $t('purchase.supplier'),
    },
    {
      fieldName: 'receiptDate',
      component: 'DatePicker',
      label: $t('purchase.receiptDate'),
    },
    {
      fieldName: 'totalAmount',
      component: 'Input',
      label: $t('purchase.totalAmount'),
    },
    {
      fieldName: 'totalGrossWeight',
      component: 'Input',
      label: $t('purchase.totalGrossWeight'),
    },
    {
      fieldName: 'totalNetWeight',
      component: 'Input',
      label: $t('purchase.totalNetWeight'),
    },
    {
      fieldName: 'confirmUserName',
      component: 'Input',
      label: $t('purchase.confirmUserName'),
      componentProps: {
        readonly: true, // 确认人字段只读
      },
    },
    {
      fieldName: 'confirmDateTime',
      component: 'Input',
      label: $t('purchase.confirmDateTime'),
      componentProps: {
        readonly: true, // 确认时间字段只读
      },
    },
    {
      fieldName: 'remark',
      component: 'Input',
      label: $t('purchase.remark'),
      componentProps: {
        readonly: false,
        type: 'textarea',
      },
    },
  ],
  // 提交处理函数
  handleSubmit: async (values) => {
    try {
      // 如果是更新模式，添加ID
      if (isUpdate.value && record.value.id) {
        values.id = record.value.id;
      }

      // 调用API保存数据
      await addOrEditImported(values);
      // 显示成功消息
      ElNotification({
        duration: 2500,
        message: isUpdate.value ? '更新成功' : '添加成功',
        type: 'success',
      });
      // 关闭弹窗
      modalApi.close();
    } catch (error) {
      console.error('保存失败:', error);
      ElNotification({ duration: 2500, message: '保存失败', type: 'error' });
    }
  },
});

/**
 * 创建模态框
 * 配置模态框的行为和事件处理
 */
const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false, // 点击遮罩层不关闭
  draggable: true, // 可拖动
  showConfirmButton: false,
  cancelText: '关闭',
  // 打开状态变化时的回调
  onOpenChange(isOpen) {
    if (isOpen) {
      // 打开时获取数据
      record.value = modalApi.getData()?.record || {};
      isUpdate.value = modalApi.getData()?.isUpdate || false;
      isView.value = modalApi.getData()?.isView || false;

      // 如果是编辑或查看模式，且有ID，则获取详细信息
      if ((isUpdate.value || isView.value) && record.value.id) {
        try {
          getImportedInfo({ id: record.value.id }).then((res) => {
            if (res) {
              // 设置表单值
              formApi.setValues(res);
            }
          });
          // 获取明细数据
          getDetailData();
        } catch {
          ElNotification({ duration: 2500, message: 'error', type: 'error' });
        }
      } else {
        // 新增模式，重置表单和明细数据
        formApi.resetForm();
        detailRowData.value = [];
      }
    } else {
      // 关闭时重置状态
      isUpdate.value = false;
      isView.value = false;
      record.value = {};
      detailRowData.value = [];
    }
  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {},
});

/**
 * 暴露组件方法
 * 直接暴露 modalApi，包含所有需要的方法
 */
defineExpose({
  modalApi,
});
</script>

<template>
  <Modal class="h-[90%] w-[90%]" :title="title">
    <div class="flex h-full flex-col">
      <!-- 主表单区域 -->
      <div class="mb-4 flex-shrink-0">
        <AddForm />
      </div>

      <!-- 明细区域 -->
      <div class="flex flex-1 flex-col">
        <div class="mb-2">
          <h3 class="text-lg font-medium">{{ $t('purchase.detailList') }}</h3>
        </div>

        <!-- 明细表格 -->
        <div class="flex-1">
          <ClientGridComponent
            ref="detailGridRef"
            :column-defs="detailColumnDefs"
            :row-data="detailRowData"
            :page-size="20"
            :default-col-def="detailDefaultColDef"
            :row-selection="detailRowSelection"
            height="100%"
          />
        </div>
      </div>
    </div>
  </Modal>
</template>
