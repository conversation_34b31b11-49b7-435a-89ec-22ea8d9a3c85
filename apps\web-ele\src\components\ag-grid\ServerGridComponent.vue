<script lang="ts" setup>
import type {
  ColDef,
  FilterModel,
  GridApi,
  GridOptions,
  RowClassParams,
  RowSelectionOptions,
  SortModelItem,
} from 'ag-grid-community';

import type { PropType } from 'vue';

import type { SupportedLanguagesType } from '@vben/locales';

import type { ActionItem } from '#/components/table-action';

import {
  computed,
  defineEmits,
  defineExpose,
  defineProps,
  onMounted,
  ref,
  watch,
} from 'vue';

import { preferences } from '@vben/preferences';

import { useFullscreen } from '@vueuse/core';
import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community';
import {
  CellSelectionModule,
  ClipboardModule,
  ColumnMenuModule,
  ContextMenuModule,
  ExcelExportModule,
  MultiFilterModule,
  ServerSideRowModelApiModule,
  ServerSideRowModelModule,
} from 'ag-grid-enterprise';
import { AgGridVue } from 'ag-grid-vue3';
import { ElButton } from 'element-plus';

import ActionCell from '#/components/ag-grid/ButtonRender.vue';
import { Icon } from '#/components/icon';
import { TableAction } from '#/components/table-action';

// Type definitions
type GetRowClass = (params: any) => string | string[] | undefined;

// 服务端数据源接口
interface ServerDataSource {
  getRows: (params: any) => Promise<{
    data: any[];
    total: number;
  }>;
}

// 组件接收的 props
const props = defineProps({
  // 简化的 API 调用函数 - 新增
  apiFunction: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: null,
  },
  // 响应数据处理函数 - 新增
  responseProcessor: {
    type: Function as PropType<
      (response: any) => { data: any[]; total: number }
    >,
    default: null,
  },
  // 搜索参数处理函数 - 新增
  searchParamsProcessor: {
    type: Function as PropType<(params: any) => any>,
    default: null,
  },
  gridOptions: {
    type: Object as () => GridOptions,
    default: () => ({
      rowModelType: 'serverSide',
      pagination: true,
      suppressPaginationPanel: false,
      paginationAutoPageSize: false,
      paginationPageSizeSelector: [10, 20, 50, 100],
      cellSelection: true,
      copyHeadersToClipboard: false,
      editType: 'fullRow',
      cacheBlockSize: 20,
      maxBlocksInCache: 10,
      getRowId: (params: any) =>
        String(params.data.id || params.data.Id || params.node.id),
      // 多列排序配置
      multiSortKey: 'ctrl', // 按住Ctrl键可以多列排序
      alwaysMultiSort: false, // 设为true则总是多列排序，不需要按Ctrl
    }),
  },
  columnDefs: {
    type: Array as () => ColDef[],
    default: () => [],
  },
  dataSource: {
    type: Object as PropType<ServerDataSource>,
    default: null, // 改为可选，因为可以使用 apiFunction
  },
  pageSize: {
    type: Number,
    default: 20,
  },
  filterModel: {
    type: Object as () => FilterModel | null,
    default: () => null,
  },
  defaultColDef: {
    type: Object as () => ColDef,
    default: () => ({
      sortable: true,
      filterParams: {
        buttons: ['reset', 'apply'],
        defaultOption: 'equals',
      },
      filter: 'agTextColumnFilter', // 默认使用文本过滤器
      resizable: true,
      flex: 1,
    }),
  },
  modules: {
    type: Array as () => any[],
    default: () => [
      AllCommunityModule,
      CellSelectionModule,
      ContextMenuModule,
      ColumnMenuModule,
      ClipboardModule,
      ExcelExportModule,
      MultiFilterModule,
      ServerSideRowModelModule,
    ],
  },
  rowSelection: {
    type: Object as () => RowSelectionOptions,
    default: () => ({
      mode: 'singleRow', // 默认单选，可选值：singleRow | multiRow
      checkboxes: false, // 默认关闭
      headerCheckbox: false, // 默认关闭
      enableClickSelection: false,
      copySelectedRows: false,
    }),
  },
  rowClassParams: {
    type: Object as () => RowClassParams,
    default: () => ({}),
  },
  getRowClass: {
    type: Function as PropType<GetRowClass>,
    default: () => (_params: any) => {
      // 默认行为：返回空的字符串或一个空的数组
      return '';
    },
  },
  height: {
    type: String,
    default: '95%',
  },
  tableActions: {
    type: Array as PropType<ActionItem[]>,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  // 是否启用内置分页处理（默认启用）
  enableBuiltinPaginationHandler: {
    type: Boolean,
    default: true,
  },
});

// 定义事件
const emit = defineEmits<{
  (event: 'dataLoaded', data: { data: any[]; total: number }): void;
  (event: 'loadError', error: any): void;
  (event: 'sortChanged', sortModel: SortModelItem[]): void;
  (event: 'filterChanged', filterModel: FilterModel): void;
  (
    event: 'paginationChanged',
    params: { pageIndex: number; pageSize: number },
  ): void;
}>();

// 注册服务端行模型 API 模块和多重过滤器模块
ModuleRegistry.registerModules([
  ServerSideRowModelApiModule,
  MultiFilterModule,
]);

// 内部状态
const searchParams = ref<any>({});
const isRefreshing = ref(false);

// 默认响应数据处理函数
const defaultResponseProcessor = (
  response: any,
): { data: any[]; total: number } => {
  // 处理返回的数据
  let responseData, responseTotal;

  if (response && (response.code === 0 || response.Code === 0)) {
    responseData = response.data || response.Data || [];
    responseTotal = response.totalCount || response.TotalCount || 0;
  } else if (Array.isArray(response)) {
    responseData = response;
    responseTotal = response.length;
  } else {
    responseData = [];
    responseTotal = 0;
  }

  return {
    data: responseData,
    total: responseTotal,
  };
};

const gridApi = ref<GridApi | null>(null);
const localeText = ref<any>(undefined);
const isLoading = ref(false);

// ✅ 使用 VueUse 全屏 API
const tableRef = ref<HTMLElement | null>(null);
const { isFullscreen, toggle } = useFullscreen(tableRef);

// 刷新表格数据，保持当前搜索条件
const handleRefresh = () => {
  if (gridApi.value) {
    // 使用 purge: true 清除缓存并重新加载数据
    // AG-Grid 会自动使用当前的搜索参数重新请求数据
    gridApi.value.refreshServerSide({ purge: true });
  }
};

// 重置过滤和排序
const handleReset = () => {
  if (gridApi.value) {
    // 清除所有过滤器
    gridApi.value.setFilterModel(null);
    // 清除所有排序
    gridApi.value.applyColumnState({
      defaultState: { sort: null },
    });
    // 刷新数据
    gridApi.value.refreshServerSide({ purge: true });
  }
};

// 进入全屏时自动调整表格宽度
watch(isFullscreen, (val) => {
  if (val && gridApi.value) {
    // 可以在这里添加全屏时的表格调整逻辑
  }
});

// 加载AG Grid的语言包
async function loadAgGridLocale(lang: SupportedLanguagesType) {
  let localeTextValue;

  switch (lang) {
    case 'en-US': {
      const { AG_GRID_LOCALE_EN } = await import('@ag-grid-community/locale');
      localeTextValue = AG_GRID_LOCALE_EN;
      break;
    }
    case 'vi-VN': {
      const { AG_GRID_LOCALE_VN } = await import('@ag-grid-community/locale');
      localeTextValue = AG_GRID_LOCALE_VN;
      break;
    }
    case 'zh-CN': {
      const { AG_GRID_LOCALE_CN } = await import('@ag-grid-community/locale');
      localeTextValue = AG_GRID_LOCALE_CN;
      break;
    }
    default: {
      const { AG_GRID_LOCALE_EN } = await import('@ag-grid-community/locale');
      localeTextValue = AG_GRID_LOCALE_EN;
      break;
    }
  }

  return localeTextValue;
}

const components = {
  actionCell: ActionCell, // 这里的 key 是你在 cellRenderer 中使用的值
};

// 服务端数据源
const serverSideDataSource = {
  getRows: async (params: any) => {
    try {
      isLoading.value = true;

      // 获取 AgGrid 当前的页面大小
      const actualPageSize = params.api
        ? params.api.paginationGetPageSize()
        : props.pageSize;
      const startRow = params.request.startRow || 0;
      const pageIndex = Math.floor(startRow / actualPageSize) + 1;

      let result: { data: any[]; total: number };

      // 如果提供了 apiFunction，使用简化的 API 调用
      if (props.apiFunction) {
        // 构建查询参数
        const queryParams: any = {
          pageIndex,
          pageSize: actualPageSize,
        };

        // 添加搜索参数
        // 总是调用 searchParamsProcessor，即使在初始化时也要处理默认参数
        if (props.searchParamsProcessor) {
          const processedSearchParams = props.searchParamsProcessor(
            searchParams.value || {},
          );
          Object.assign(queryParams, processedSearchParams);
        } else if (
          searchParams.value &&
          Object.keys(searchParams.value).length > 0
        ) {
          Object.assign(queryParams, searchParams.value);
        }

        // 处理 AG-Grid 排序参数
        if (params.request.sortModel && params.request.sortModel.length > 0) {
          queryParams.sortModel = params.request.sortModel.map(
            (sort: any, index: number) => ({
              field: sort.colId,
              direction: sort.sort,
              priority: index + 1,
            }),
          );
        }

        // 处理 AG-Grid 列筛选器
        if (
          params.request.filterModel &&
          Object.keys(params.request.filterModel).length > 0
        ) {
          const filterModel = params.request.filterModel;
          const filters = [];

          // 将 AG-Grid 的筛选器模型转换为统一的筛选器数组
          for (const [fieldName, filterConfig] of Object.entries(filterModel)) {
            if (filterConfig && typeof filterConfig === 'object') {
              const filter = filterConfig as any;

              // 处理单个筛选条件
              if (
                (filter.filterType === 'text' ||
                  filter.filterType === 'date' ||
                  filter.filterType === 'number') &&
                filter.type
              ) {
                // 对于 blank 和 notBlank 筛选，不需要 filter 值
                if (filter.type === 'blank' || filter.type === 'notBlank') {
                  filters.push({
                    column: fieldName,
                    filterType: filter.type,
                    value: null, // 空白筛选不需要值
                  });
                }
                // 处理日期过滤器
                else if (filter.filterType === 'date') {
                  // 处理日期范围筛选 (inRange)
                  if (
                    filter.type === 'inRange' &&
                    filter.dateFrom &&
                    filter.dateTo
                  ) {
                    filters.push({
                      column: fieldName,
                      filterType: 'inRange',
                      value: {
                        from: filter.dateFrom,
                        to: filter.dateTo,
                      },
                    });
                  }
                  // 处理单个日期筛选 (equals, lessThan, greaterThan 等)
                  else if (filter.dateFrom) {
                    filters.push({
                      column: fieldName,
                      filterType: filter.type,
                      value: filter.dateFrom,
                    });
                  }
                }
                // 处理文本和数字过滤器
                else if (
                  filter.filter !== undefined &&
                  filter.filter !== null &&
                  filter.filter !== ''
                ) {
                  filters.push({
                    column: fieldName,
                    filterType: filter.type, // equals, contains, startsWith, endsWith, lessThan, greaterThan 等
                    value: filter.filter,
                  });
                }
              }
              // 处理集合筛选器（agSetColumnFilter）
              else if (
                filter.filterType === 'set' &&
                filter.values &&
                Array.isArray(filter.values) &&
                filter.values.length > 0
              ) {
                filters.push({
                  column: fieldName,
                  filterType: 'in',
                  value: filter.values,
                });
              }
              // 处理组合筛选条件（多个条件用 AND/OR 连接）
              else if (
                filter.operator &&
                filter.conditions &&
                Array.isArray(filter.conditions)
              ) {
                for (const condition of filter.conditions) {
                  if (condition.type) {
                    // 对于 blank 和 notBlank 筛选，不需要 filter 值
                    if (
                      condition.type === 'blank' ||
                      condition.type === 'notBlank'
                    ) {
                      filters.push({
                        column: fieldName,
                        filterType: condition.type,
                        value: null,
                        operator: filter.operator, // AND 或 OR
                      });
                    }
                    // 处理日期过滤器在组合条件中
                    else if (filter.filterType === 'date') {
                      // 处理日期范围筛选 (inRange) 在组合条件中
                      if (
                        condition.type === 'inRange' &&
                        condition.dateFrom &&
                        condition.dateTo
                      ) {
                        filters.push({
                          column: fieldName,
                          filterType: 'inRange',
                          value: {
                            from: condition.dateFrom,
                            to: condition.dateTo,
                          },
                          operator: filter.operator, // AND 或 OR
                        });
                      }
                      // 处理单个日期筛选在组合条件中
                      else if (condition.dateFrom) {
                        filters.push({
                          column: fieldName,
                          filterType: condition.type,
                          value: condition.dateFrom,
                          operator: filter.operator, // AND 或 OR
                        });
                      }
                    }
                    // 对于文本和数字筛选类型，需要有筛选值
                    else if (
                      condition.filter !== undefined &&
                      condition.filter !== null &&
                      condition.filter !== ''
                    ) {
                      filters.push({
                        column: fieldName,
                        filterType: condition.type,
                        value: condition.filter,
                        operator: filter.operator, // AND 或 OR
                      });
                    }
                  }
                }
              }
            }
          }

          // 将筛选器数组作为一个整体参数传递
          if (filters.length > 0) {
            queryParams.filters = filters;
          }
        }

        // 调用 API
        const response = await props.apiFunction(queryParams);

        // 处理响应数据
        const responseProcessor =
          props.responseProcessor || defaultResponseProcessor;
        result = responseProcessor(response);
      } else if (props.dataSource) {
        // 使用传统的 dataSource 方式
        const requestParams = {
          startRow,
          endRow: params.request.endRow,
          sortModel: params.request.sortModel,
          filterModel: params.request.filterModel,
          pageSize: actualPageSize,
          pageIndex,
        };
        result = await props.dataSource.getRows(requestParams);
      } else {
        throw new Error('必须提供 apiFunction 或 dataSource');
      }

      // 触发数据加载完成事件
      emit('dataLoaded', result);

      // 成功回调
      params.success({
        rowData: result.data,
        rowCount: result.total,
      });
    } catch (error) {
      emit('loadError', error);
      params.fail();
    } finally {
      isLoading.value = false;
    }
  },
};

// 通用的过滤器参数配置
const getFilterParams = (extraParams = {}) => ({
  buttons: ['reset', 'apply'],
  defaultOption: 'equals',
  suppressAndOrCondition: true,
  ...extraParams,
});

// 根据列的 filterType 字段自动设置过滤器（避免与 AG-Grid 的 type 字段冲突）
const processedColumnDefs = computed(() => {
  return props.columnDefs.map((col) => {
    // 如果列已经明确指定了过滤器，则不修改
    if (col.filter) {
      return col;
    }

    // 根据自定义的 filterType 字段设置过滤器（避免与 AG-Grid 的 type 字段冲突）
    const columnType = (col as any).filterType || (col as any).type;

    // 创建新的列定义，移除自定义的 filterType 属性
    const { filterType: _filterType, ...cleanCol } = col as any;

    switch (columnType) {
      case 'date': {
        return {
          ...cleanCol,
          filter: 'agDateColumnFilter',
          filterParams: getFilterParams({
            browserDatePicker: true,
            ...cleanCol.filterParams,
          }),
        };
      }

      case 'number': {
        return {
          ...cleanCol,
          filter: 'agNumberColumnFilter',
          filterParams: getFilterParams(cleanCol.filterParams),
        };
      }

      default: {
        return {
          ...cleanCol,
          filter: 'agTextColumnFilter',
          filterParams: getFilterParams(cleanCol.filterParams),
        };
      }
    }
  });
});

// 合并gridOptions
const mergedGridOptions = computed(() => {
  return {
    ...props.gridOptions,
    paginationPageSize: props.pageSize,
    cacheBlockSize: props.pageSize, // 确保缓存块大小与页面大小一致
    serverSideDatasource: serverSideDataSource,
  };
});

// 初始化语言设置
onMounted(async () => {
  // 加载当前语言的AG Grid本地化文本
  localeText.value = await loadAgGridLocale(preferences.app.locale);
});

// 监听语言变化
watch(
  () => preferences.app.locale,
  async (newLocale) => {
    // 加载新语言的AG Grid本地化文本
    localeText.value = await loadAgGridLocale(
      newLocale as SupportedLanguagesType,
    );
  },
);

// 事件：Grid Ready 时设置数据源
const onGridReady = (params: any) => {
  gridApi.value = params.api;

  // 服务端数据源已经在 mergedGridOptions 中配置，不需要在这里再次设置

  // 调用从 props 传入的 onGridReady 回调（如果存在）
  if (props.gridOptions?.onGridReady) {
    props.gridOptions.onGridReady(params);
  }
};

// 排序变化事件
const onSortChanged = (params: any) => {
  const sortModel = params.api.getSortModel();
  emit('sortChanged', sortModel);
};

// 过滤变化事件
const onFilterChanged = (params: any) => {
  const filterModel = params.api.getFilterModel();
  emit('filterChanged', filterModel);
};

// 分页变化事件处理 - 内置简化版本
const onPaginationChanged = (params: any) => {
  const pageSize = params.api.paginationGetPageSize();
  const pageIndex = params.api.paginationGetCurrentPage() + 1; // AG-Grid 页码从0开始，我们转换为从1开始

  // 内置的分页变化处理逻辑（如果启用）
  if (props.enableBuiltinPaginationHandler) {
    // 这里可以添加更多内置的分页处理逻辑
    // 比如：保存用户偏好、更新 URL 参数等
  }

  // 始终触发分页变化事件，让父组件可以进行自定义处理（如果需要）
  emit('paginationChanged', { pageSize, pageIndex });
};

// 获取当前页面大小的方法
const getCurrentPageSize = () => {
  if (gridApi.value) {
    return gridApi.value.paginationGetPageSize();
  }
  return props.pageSize;
};

// 搜索方法 - 新增
const search = (params: any) => {
  if (isRefreshing.value) {
    return;
  }

  isRefreshing.value = true;

  // 从搜索参数中移除 pageSize，避免传递给后端
  const { pageSize: _pageSize, ...searchData } = params;
  searchParams.value = searchData;

  // 刷新表格数据
  handleRefresh();

  // 延迟重置标志
  setTimeout(() => {
    isRefreshing.value = false;
  }, 1000);
};

// 获取选中的行
const getSelectedRows = () => {
  return gridApi.value?.getSelectedRows() || [];
};

// 暴露全屏相关功能和 gridApi
defineExpose({
  gridApi,
  isFullscreen,
  toggle,
  handleRefresh,
  handleReset,
  isLoading,
  getCurrentPageSize,
  search,
  getSelectedRows,
});
</script>

<template>
  <div
    ref="tableRef"
    class="relative h-full w-full"
    :class="{
      'fullscreen-container': isFullscreen,
      'bg-white': isFullscreen,
    }"
    v-loading="props.loading || isLoading"
    element-loading-text="数据加载中..."
  >
    <!-- 顶部操作区域 -->
    <div
      class="mb-2 flex items-center justify-between"
      :class="{ 'px-4 pt-4': isFullscreen }"
    >
      <!-- 左侧：TableAction 按钮组（如果有的话） -->
      <div>
        <TableAction
          v-if="tableActions && tableActions.length > 0"
          :actions="tableActions"
        />
      </div>

      <!-- 右侧：表格操作按钮 - 始终显示 -->
      <div class="flex gap-2">
        <!-- 重置过滤和排序按钮 -->
        <ElButton
          circle
          size="small"
          @click="handleReset"
          title="重置过滤和排序"
        >
          <Icon icon="ant-design:clear-outlined" />
        </ElButton>
        <!-- 刷新按钮 -->
        <ElButton
          circle
          size="small"
          @click="handleRefresh"
          title="刷新表格"
          :loading="isLoading"
        >
          <Icon icon="ant-design:reload-outlined" />
        </ElButton>
        <!-- 全屏切换按钮 -->
        <ElButton
          circle
          size="small"
          @click="toggle"
          :title="isFullscreen ? '退出全屏' : '进入全屏'"
        >
          <Icon
            :icon="
              isFullscreen
                ? 'ant-design:fullscreen-exit-outlined'
                : 'ant-design:fullscreen-outlined'
            "
          />
        </ElButton>
      </div>
    </div>

    <!-- ag-grid 表格 -->
    <div
      :class="{ 'px-4 pb-4': isFullscreen }"
      :style="{ height: isFullscreen ? '100% - 60px' : 'calc(100% - 60px)' }"
    >
      <AgGridVue
        class="ag-theme-alpine"
        :grid-options="mergedGridOptions"
        :column-defs="processedColumnDefs"
        :locale-text="localeText"
        :modules="modules"
        :height="height"
        :row-selection="rowSelection"
        :components="components"
        :default-col-def="defaultColDef"
        :row-class-params="rowClassParams"
        :get-row-class="getRowClass"
        :style="{
          width: '100%',
          height: '100%',
          minHeight: '300px',
        }"
        @grid-ready="onGridReady"
        @sort-changed="onSortChanged"
        @filter-changed="onFilterChanged"
        @pagination-changed="onPaginationChanged"
      />
    </div>
  </div>
</template>

<style>
.import-error {
  background-color: #f8d7da !important;
}

/* 全屏容器样式 - 只在全屏时应用 */
.fullscreen-container {
  background-color: #f5f7fa !important;
}

/* 全屏时确保表格有合适的背景 */
.fullscreen-container .ag-theme-alpine {
  background-color: #fff !important;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

/* 加载状态样式 */
.el-loading-mask {
  background-color: rgb(255 255 255 / 80%) !important;
}
</style>
