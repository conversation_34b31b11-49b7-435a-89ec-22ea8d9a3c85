# ServerGridComponent 服务端表格组件使用指南

## 组件介绍

`ServerGridComponent` 是一个基于 AG-Grid 的服务端数据加载表格组件，适用于大数据量场景，支持服务端分页、排序和过滤。

## 基本用法

```vue
<template>
  <ServerGridComponent
    :dataSource="dataSource"
    :columnDefs="columnDefs"
    :pageSize="20"
    :loading="loading"
    @dataLoaded="handleDataLoaded"
    @loadError="handleLoadError"
    @sortChanged="handleSortChanged"
    @filterChanged="handleFilterChanged"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ServerGridComponent } from '@/components/ag-grid/ServerGridComponent.vue';
import { useRequest } from '@/hooks/useRequest';

// 表格列定义
const columnDefs = ref([
  { headerName: '编号', field: 'id', width: 80 },
  { headerName: '名称', field: 'name', flex: 1 },
  { headerName: '状态', field: 'status', width: 100 },
  {
    headerName: '操作',
    cellRenderer: 'actionCell',
    width: 150,
    cellRendererParams: {
      actions: [
        {
          label: '编辑',
          type: 'primary',
          callback: (data) => handleEdit(data),
        },
        {
          label: '删除',
          type: 'danger',
          callback: (data) => handleDelete(data),
          popConfirm: {
            title: '确认删除',
            description: '确定要删除此记录吗？',
            confirm: (data) => confirmDelete(data),
          },
        },
      ],
    },
  },
]);

// 加载状态
const loading = ref(false);

// 数据源定义
const dataSource = {
  getRows: async (params) => {
    // 这里可以调用您的API服务
    const { startRow, endRow, sortModel, filterModel, pageIndex, pageSize } = params;

    try {
      loading.value = true;

      // 调用后端API
      const response = await yourApiService.getList({
        pageIndex,
        pageSize,
        sortField: sortModel?.length > 0 ? sortModel[0].colId : undefined,
        sortOrder: sortModel?.length > 0 ? sortModel[0].sort : undefined,
        filters: filterModel,
      });

      return {
        data: response.data.list,
        total: response.data.total,
      };
    } finally {
      loading.value = false;
    }
  },
};

// 事件处理
const handleDataLoaded = (result) => {
  console.log('数据加载完成', result);
};

const handleLoadError = (error) => {
  console.error('数据加载失败', error);
};

const handleSortChanged = (sortModel) => {
  console.log('排序变化', sortModel);
};

const handleFilterChanged = (filterModel) => {
  console.log('过滤条件变化', filterModel);
};

// 操作按钮回调
const handleEdit = (data) => {
  console.log('编辑', data);
};

const handleDelete = (data) => {
  console.log('删除', data);
};

const confirmDelete = async (data) => {
  try {
    await yourApiService.delete(data.id);
    // 刷新表格
    gridRef.value.handleRefresh();
  } catch (error) {
    console.error('删除失败', error);
  }
};
</script>
```

## 下拉菜单操作按钮

```vue
<template>
  <ServerGridComponent
    ref="gridRef"
    :dataSource="dataSource"
    :columnDefs="columnDefs"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ServerGridComponent } from '@/components/ag-grid/ServerGridComponent.vue';

const gridRef = ref();

const columnDefs = ref([
  // ... 其他列定义
  {
    headerName: '操作',
    cellRenderer: 'actionCell',
    width: 200,
    cellRendererParams: {
      actions: {
        // 下拉菜单配置
        primaryActions: [
          {
            label: '查看',
            type: 'primary',
            callback: (data) => handleView(data),
          },
        ],
        menuItems: [
          {
            label: '编辑',
            callback: (data) => handleEdit(data),
          },
          {
            label: '删除',
            callback: (data) => handleDelete(data),
            popConfirm: {
              title: '确认删除',
              description: '确定要删除此记录吗？',
              confirm: (data) => confirmDelete(data),
            },
          },
          {
            label: '导出',
            callback: (data) => handleExport(data),
            divided: true, // 添加分割线
          },
        ],
      },
    },
  },
]);

// ... 数据源定义和事件处理
</script>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| dataSource | Object | - | 必填，服务端数据源对象，需要实现 getRows 方法 |
| columnDefs | Array | [] | 列定义数组 |
| pageSize | Number | 20 | 每页显示的记录数 |
| gridOptions | Object | - | AG-Grid 配置选项 |
| filterModel | Object | null | 过滤模型 |
| defaultColDef | Object | - | 默认列配置 |
| modules | Array | [...] | AG-Grid 模块 |
| rowSelection | Object | - | 行选择配置 |
| height | String | '95%' | 表格高度 |
| tableActions | Array | [] | 表格顶部操作按钮 |
| loading | Boolean | false | 加载状态 |

## 组件事件

| 事件名        | 参数            | 说明             |
| ------------- | --------------- | ---------------- |
| dataLoaded    | { data, total } | 数据加载完成事件 |
| loadError     | error           | 数据加载失败事件 |
| sortChanged   | sortModel       | 排序变化事件     |
| filterChanged | filterModel     | 过滤条件变化事件 |

## 组件方法

通过 ref 可以访问以下方法：

| 方法名        | 参数 | 说明         |
| ------------- | ---- | ------------ |
| handleRefresh | -    | 刷新表格数据 |
| toggle        | -    | 切换全屏显示 |

## 注意事项

1. 服务端组件需要实现 `dataSource.getRows` 方法，该方法接收分页、排序和过滤参数，并返回 Promise 对象，包含 data 和 total 属性。
2. 组件内部已处理加载状态，可通过 `loading` 属性控制外部加载状态。
3. 表格操作按钮使用方式与客户端组件完全一致，支持普通按钮和下拉菜单两种模式。
4. 服务端组件使用了 AG-Grid 的 ServerSideRowModelModule 模块，确保已正确引入该模块。
