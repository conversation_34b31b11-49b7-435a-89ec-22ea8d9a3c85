# ServerGridComponent 简化前后对比

## 简化的成果

通过对 ServerGridComponent 的改进，我们实现了以下目标：

1. **减少重复代码**：从 90+ 行的 dataSource 实现减少到 1 行的 API 函数传递
2. **简化调用方式**：内置常用功能，让调用更简单
3. **保持向后兼容**：原有的 dataSource 方式仍然可用
4. **增强功能**：内置搜索参数管理、响应数据处理等

## 代码对比

### 原始方式（复杂）

```vue
<script lang="ts" setup>
// 需要手动管理的状态
const searchParams = ref<any>({});
const currentPageSize = ref(20);
const isRefreshing = ref(false);

// 需要手动编写复杂的 dataSource
const dataSource = {
  getRows: async (params: any) => {
    try {
      console.warn('=== 数据源被调用 ===', params);
      const { startRow, pageIndex, pageSize } = params;

      // 使用 ServerGridComponent 传递过来的页面大小
      const actualPageSize = pageSize || currentPageSize.value;

      console.warn('=== 数据源中的页面大小检查 ===');
      console.warn('传入的 pageSize:', pageSize);
      console.warn('currentPageSize.value:', currentPageSize.value);
      console.warn('最终使用的 actualPageSize:', actualPageSize);

      // 构建查询参数
      const queryParams: any = {
        pageIndex:
          pageIndex || Math.floor((startRow || 0) / actualPageSize) + 1,
        pageSize: actualPageSize,
      };

      console.warn('构建的查询参数:', queryParams);

      // 添加搜索表单的参数
      if (searchParams.value) {
        Object.assign(queryParams, searchParams.value);
      }

      console.warn('发送给API的参数:', queryParams);

      const res = await getMaterialList(queryParams);
      console.warn('API返回的数据:', res);

      // 处理返回的数据
      let responseData, responseTotal;

      if (res && (res.code === 0 || res.Code === 0)) {
        responseData = res.data || res.Data || [];
        responseTotal = res.totalCount || res.TotalCount || 0;
      } else if (Array.isArray(res)) {
        responseData = res;
        responseTotal = res.length;
      } else {
        responseData = [];
        responseTotal = 0;
      }

      console.warn('最终返回的数据:', {
        data: responseData,
        total: responseTotal,
      });

      return {
        data: responseData,
        total: responseTotal,
      };
    } catch (error) {
      console.error('获取数据失败:', error);
      throw error;
    }
  },
};

// 复杂的搜索处理
const [QueryForm] = useVbenForm({
  handleSubmit: (values) => {
    console.warn('=== 查询表单提交开始 ===', values);

    // 防止重复请求
    if (isRefreshing.value) {
      console.warn('正在刷新中，跳过此次请求');
      return;
    }

    isRefreshing.value = true;

    // 处理页面大小和搜索参数
    if (values.pageSize) {
      console.warn('用户选择的页面大小:', values.pageSize);
      currentPageSize.value = Number(values.pageSize);
      console.warn('currentPageSize 更新为:', currentPageSize.value);
    }

    // 从搜索参数中移除 pageSize，避免传递给后端
    const { pageSize: _pageSize, ...searchData } = values;
    searchParams.value = searchData;

    console.warn('搜索参数:', searchParams.value);
    console.warn('当前页面大小:', currentPageSize.value);

    // 刷新表格数据
    if (gridRef.value && gridRef.value.handleRefresh) {
      console.warn('调用表格刷新');
      gridRef.value.handleRefresh();

      // 延迟重置标志
      setTimeout(() => {
        isRefreshing.value = false;
      }, 1000);
    } else {
      console.error('gridRef 或 handleRefresh 方法不存在');
      isRefreshing.value = false;
    }

    console.warn('=== 查询表单提交结束 ===');
  },
  // ... 其他配置
});

// 复杂的分页处理
const handlePaginationChanged = (params: {
  pageIndex: number;
  pageSize: number;
}) => {
  console.warn('AgGrid 分页变化:', params);
  // 同步页面大小到我们的状态
  currentPageSize.value = params.pageSize;
};
</script>

<template>
  <ServerGridComponent
    ref="gridRef"
    :column-defs="columnDefs"
    :data-source="dataSource"
    :page-size="currentPageSize"
    :default-col-def="defaultColDef"
    :row-selection="rowSelection"
    :table-actions="[]"
    @data-loaded="handleDataLoaded"
    @load-error="handleLoadError"
    @pagination-changed="handlePaginationChanged"
  />
</template>
```

### 简化后的方式（简单）

```vue
<script lang="ts" setup>
// 不需要手动管理状态，组件内部处理

// 简化的搜索处理
const [QueryForm] = useVbenForm({
  handleSubmit: (values) => {
    // 直接调用表格的搜索方法，一行搞定
    gridRef.value?.search(values);
  },
  // ... 其他配置
});

// 不需要复杂的事件处理
const handleDataLoaded = (result: { data: any[]; total: number }) => {
  console.log('数据加载完成:', result);
};

const handleLoadError = (error: any) => {
  console.error('数据加载失败:', error);
  useMessage().showMessage('error', '数据加载失败，请重试');
};
</script>

<template>
  <!-- 超级简化的调用方式 -->
  <ServerGridComponent
    ref="gridRef"
    :api-function="getMaterialList"
    :column-defs="columnDefs"
    :default-col-def="defaultColDef"
    :row-selection="rowSelection"
    :table-actions="[]"
    @data-loaded="handleDataLoaded"
    @load-error="handleLoadError"
  />
</template>
```

## 主要改进点

### 1. 代码行数对比
- **原始方式**：~120 行代码（包含 dataSource、状态管理、事件处理）
- **简化方式**：~15 行代码（只需要事件处理）
- **减少了约 87% 的代码量**

### 2. 功能对比

| 功能 | 原始方式 | 简化方式 |
|------|----------|----------|
| API 调用 | 手动编写 dataSource.getRows | 直接传入 apiFunction |
| 响应数据处理 | 手动处理各种响应格式 | 内置处理常见格式 |
| 搜索参数管理 | 手动管理 searchParams | 内置管理 |
| 分页处理 | 手动同步页面大小 | 内置处理 |
| 防重复请求 | 手动实现 isRefreshing | 内置处理 |
| 获取选中行 | 手动访问 gridApi | 提供 getSelectedRows 方法 |

### 3. 新增的便利功能

1. **内置搜索方法**：`gridRef.value?.search(params)`
2. **内置选中行获取**：`gridRef.value?.getSelectedRows()`
3. **自动响应数据处理**：支持常见的 API 响应格式
4. **自定义处理器**：支持 responseProcessor 和 searchParamsProcessor

### 4. 向后兼容性

- 原有的 `dataSource` 方式仍然完全支持
- 所有原有的 props 和事件都保持不变
- 可以逐步迁移，不需要一次性改完

## 使用建议

1. **新项目**：直接使用简化的 `apiFunction` 方式
2. **现有项目**：可以逐步迁移，先保持原有方式不变
3. **复杂需求**：可以结合使用，比如使用 `apiFunction` + 自定义处理器

这样的改进让 ServerGridComponent 的调用变得更加简单和直观，同时保持了强大的功能和灵活性！
