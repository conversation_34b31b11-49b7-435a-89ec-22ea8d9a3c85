<script lang="ts" setup>
import { computed, ref } from 'vue';

import {
  SvgBellIcon,
  SvgBuilding,
  SvgCheckCircle,
  SvgDollarCircle,
  SvgFileText,
} from '@vben/icons';
import { $t } from '@vben/locales';

import { ElNotification } from 'element-plus';

import { useVbenForm } from '#/adapter/form';
import { ExpandableCardGroup } from '#/components/expandable-card';
import selectData from '#/views/common/selectData.vue';

// 定义 props
interface Props {
  record: any;
  isUpdate: boolean;
}
const props = defineProps<Props>();
const selectDataRef = ref();
const selectExportDataRef = ref();
// 计算属性，避免频繁的响应式更新
// 只在记录ID变化时重新渲染，避免编辑状态变化导致的重新渲染
const formKey = computed(() => `customs-${props.record?.id || 'new'}`);

// 响应式的只读状态
const isReadonly = computed(() => !props.isUpdate);

// 卡片配置
const cardConfigs = [
  {
    title: '进口公司',
    icon: SvgBellIcon,
    iconClass: 'export-icon',
    defaultExpanded: true,
  },
  {
    title: '出口公司',
    icon: SvgBuilding,
    iconClass: 'import-icon',
    defaultExpanded: true,
  },
  {
    title: '提单信息',
    icon: SvgFileText,
    iconClass: 'bill-icon',
    defaultExpanded: true,
  },
  {
    title: '许可证信息',
    icon: SvgCheckCircle,
    iconClass: 'permit-icon',
    defaultExpanded: true,
  },
  {
    title: '发票',
    icon: SvgDollarCircle,
    iconClass: 'invoice-icon',
    defaultExpanded: true,
  },
  {
    title: '税务信息',
    icon: SvgCheckCircle,
    iconClass: 'tax-icon',
    defaultExpanded: true,
  },
];

/**
 * 出口公司信息表单
 */
const [ExportCompanyForm, exportCompanyFormApi] = useVbenForm({
  showDefaultActions: false,
  commonConfig: {
    componentProps: {
      class: 'w-full',
      readonly: isReadonly,
    },
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  schema: [
    {
      label: '公司代码',
      fieldName: 'exportCompanyCode',
      component: 'SearchInput',
      componentProps: {
        searchButtonProps: {
          disabled: isReadonly,
        },
        onSearch: async () => {
          await selectExportCompanyOpen();
        },
      },
    },
    {
      fieldName: 'exportTaxCode',
      component: 'Input',
      label: '公司税号',
      componentProps: {
        placeholder: '请输入出口公司税号',
      },
    },
    {
      fieldName: 'exportCompanyName',
      component: 'Input',
      label: '公司名称',
      componentProps: {
        placeholder: '请输入出口公司名称',
      },
    },
    {
      fieldName: 'exportPostalCode',
      component: 'Input',
      label: '邮政编码',
      componentProps: {
        placeholder: '请输入邮政编码',
      },
    },
    {
      fieldName: 'exportAddress',
      component: 'Input',
      label: '地址',
      componentProps: {
        placeholder: '请输入公司地址',
      },
    },
    {
      fieldName: 'exportCountryCode',
      component: 'Input',
      label: '国家代码',
      componentProps: {
        placeholder: '请输入国家代码',
      },
    },
  ],
});

/**
 * 进口公司信息表单
 */
const [ImportCompanyForm, importCompanyFormApi] = useVbenForm({
  showDefaultActions: false,
  commonConfig: {
    componentProps: {
      class: 'w-full',
      readonly: isReadonly,
    },
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:col-span-4',
  schema: [
    {
      label: '公司代码',
      fieldName: 'importCompanyCode',
      component: 'SearchInput',
      componentProps: {
        searchButtonProps: {
          disabled: isReadonly,
        },
        onSearch: async () => {
          await selectOpen();
        },
      },
    },
    {
      fieldName: 'importTaxCode',
      component: 'Input',
      label: '公司税号',
      componentProps: {
        placeholder: '请输入进口公司税号',
      },
    },
    {
      fieldName: 'importCompanyName',
      component: 'Input',
      label: '公司名称',
      componentProps: {
        placeholder: '请输入进口公司名称',
      },
    },
    {
      fieldName: 'importPostalCode',
      component: 'Input',
      label: '邮政编码',
      componentProps: {
        placeholder: '请输入邮政编码',
      },
    },
    {
      fieldName: 'importAddress',
      component: 'Input',
      label: '地址',
      componentProps: {
        placeholder: '请输入公司地址',
      },
    },
    {
      fieldName: 'importPhone',
      component: 'Input',
      label: '电话号码',
      componentProps: {
        placeholder: '请输入公司电话号码',
      },
    },
    {
      fieldName: 'importContactPerson',
      component: 'Input',
      label: '联系人名称',
      componentProps: {
        placeholder: '请输入联系人姓名',
      },
    },
    {
      fieldName: 'importContactPhone',
      component: 'Input',
      label: '联系人电话号码',
      componentProps: {
        placeholder: '请输入联系人电话号码',
      },
    },
    {
      fieldName: 'importEmail',
      component: 'Input',
      label: '邮件',
      componentProps: {
        placeholder: '请输入邮箱地址',
      },
    },
    {
      fieldName: 'importTypeCode',
      component: 'Select',
      label: '类型代码',
      componentProps: {
        clearable: true,
        getPopupContainer: () => document.body,
        placeholder: '请选择公司类型代码',
        options: [
          { label: '生产企业', value: '1' },
          { label: '贸易公司', value: '2' },
          { label: '其他', value: '3' },
        ],
      },
    },
    {
      fieldName: 'importCustomsAuthority',
      component: 'Input',
      label: '海关当局',
      componentProps: {
        placeholder: '请输入海关当局',
      },
    },
    {
      fieldName: 'importGoodsClassificationCode',
      component: 'Input',
      label: '货物分类代码',
      componentProps: {
        placeholder: '请输入货物分类代码',
      },
    },
    {
      fieldName: 'importPersonOrgClassification',
      component: 'Input',
      label: '个人/组织分类',
    },
    {
      fieldName: 'importDeclarationDeptCode',
      component: 'Input',
      label: '申报单处理部门代码',
      componentProps: {
        placeholder: '请输入申报单处理部门代码',
      },
    },
    {
      fieldName: 'importTransportModeCode',
      component: 'Input',
      label: '运输方式代码',
      componentProps: {
        placeholder: '请输入运输方式代码',
      },
    },
  ],
});

/**
 * 提单信息表单
 */
const [BillOfLadingForm, billOfLadingFormApi] = useVbenForm({
  showDefaultActions: false,
  commonConfig: {
    componentProps: {
      class: 'w-full',
      readonly: isReadonly,
    },
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  schema: [
    {
      fieldName: 'billNo',
      component: 'Input',
      label: '提单号',
      componentProps: {
        placeholder: '请输入提单号',
      },
    },
    {
      fieldName: 'atd',
      component: 'DatePicker',
      label: '开航日期（ATD）',
      componentProps: {
        placeholder: '请选择开航日期',
      },
    },
    {
      fieldName: 'packageCount',
      component: 'InputNumber',
      label: '件数',
      componentProps: {
        placeholder: '请输入件数',
        min: 0,
      },
    },
    {
      fieldName: 'grossWeight',
      component: 'InputNumber',
      label: '货物毛重',
      componentProps: {
        placeholder: '请输入货物毛重(kg)',
        min: 0,
        precision: 2,
      },
    },
    {
      fieldName: 'warehouseLocationCode',
      component: 'Input',
      label: '预期仓库位置代码',
      componentProps: {
        placeholder: '请输入预期仓库位置代码',
      },
    },
    {
      fieldName: 'transportMode',
      component: 'Select',
      label: '运输方式',
      componentProps: {
        placeholder: '请选择运输方式',
        options: [
          { label: '海运', value: 'sea' },
          { label: '空运', value: 'air' },
          { label: '陆运', value: 'land' },
          { label: '铁路', value: 'rail' },
        ],
      },
    },
    {
      fieldName: 'ata',
      component: 'DatePicker',
      label: '到货日期（ATA）',
      componentProps: {
        placeholder: '请选择到货日期',
      },
    },
    {
      fieldName: 'dischargePort',
      component: 'Input',
      label: '卸货港',
      componentProps: {
        placeholder: '请输入卸货港',
      },
    },
    {
      fieldName: 'loadingPort',
      component: 'Input',
      label: '装货港',
      componentProps: {
        placeholder: '请输入装货港',
      },
    },
    {
      fieldName: 'containerCount',
      component: 'InputNumber',
      label: '集装箱数量',
      componentProps: {
        placeholder: '请输入集装箱数量',
        min: 0,
      },
    },
  ],
});

/**
 * 许可证信息表单
 */
const [PermitForm, permitFormApi] = useVbenForm({
  showDefaultActions: false,
  commonConfig: {
    componentProps: {
      class: 'w-full',
      readonly: isReadonly,
    },
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  schema: [
    {
      fieldName: 'licenseContractNo',
      component: 'Input',
      label: '加工合同单号',
      componentProps: {
        placeholder: '请输入加工合同单号',
      },
    },
    {
      fieldName: 'licenseContractDate',
      component: 'DatePicker',
      label: '合同时间',
      componentProps: {
        placeholder: '请选择合同时间',
      },
    },
    {
      fieldName: 'licenseExpiryDate',
      component: 'DatePicker',
      label: '合同到期日',
      componentProps: {
        placeholder: '请选择合同到期日',
      },
    },
    {
      fieldName: 'licenseImportNo',
      component: 'Input',
      label: '进口许可证',
      componentProps: {
        placeholder: '请输入进口许可证号',
      },
    },
  ],
});

/**
 * 发票信息表单
 */
const [InvoiceForm, invoiceFormApi] = useVbenForm({
  showDefaultActions: false,
  commonConfig: {
    componentProps: {
      class: 'w-full',
      readonly: isReadonly,
    },
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  schema: [
    {
      fieldName: 'invoiceType',
      component: 'Select',
      label: '发票分类',
      componentProps: {
        placeholder: '请选择发票分类',
        options: [
          { label: '商业发票', value: 'commercial' },
          { label: '形式发票', value: 'proforma' },
          { label: '其他', value: 'other' },
        ],
      },
    },
    {
      fieldName: 'invoiceNo',
      component: 'Input',
      label: '发票号码',
      componentProps: {
        placeholder: '请输入发票号码',
      },
    },
    {
      fieldName: 'invoiceDate',
      component: 'DatePicker',
      label: '开具日期',
      componentProps: {
        placeholder: '请选择开具日期',
      },
    },
    {
      fieldName: 'paymentMethod',
      component: 'Select',
      label: '付款方式',
      componentProps: {
        placeholder: '请选择付款方式',
        options: [
          { label: 'T/T电汇', value: 'tt' },
          { label: 'L/C信用证', value: 'lc' },
          { label: 'D/P付款交单', value: 'dp' },
          { label: 'D/A承兑交单', value: 'da' },
          { label: '现金', value: 'cash' },
        ],
      },
    },
    {
      fieldName: 'invoiceTypeCode',
      component: 'Input',
      label: '发票分类代码',
      componentProps: {
        placeholder: '请输入发票分类代码',
      },
    },
    {
      fieldName: 'invoiceAmount',
      component: 'InputNumber',
      label: '发票金额',
      componentProps: {
        placeholder: '请输入发票金额',
        min: 0,
        precision: 2,
      },
    },
    {
      fieldName: 'deliveryTerms',
      component: 'Select',
      label: '交货条款',
      componentProps: {
        placeholder: '请选择交货条款',
        options: [
          { label: 'FOB', value: 'fob' },
          { label: 'CIF', value: 'cif' },
          { label: 'CFR', value: 'cfr' },
          { label: 'EXW', value: 'exw' },
          { label: 'FCA', value: 'fca' },
          { label: 'CPT', value: 'cpt' },
          { label: 'CIP', value: 'cip' },
        ],
      },
    },
    {
      fieldName: 'currency',
      component: 'Select',
      label: '货币单位',
      componentProps: {
        placeholder: '请选择货币单位',
        options: [
          { label: '人民币(CNY)', value: 'CNY' },
          { label: '美元(USD)', value: 'USD' },
          { label: '欧元(EUR)', value: 'EUR' },
          { label: '日元(JPY)', value: 'JPY' },
          { label: '英镑(GBP)', value: 'GBP' },
        ],
      },
    },
    {
      fieldName: 'customsValueCode',
      component: 'Input',
      label: '价值申报的分类代码',
      componentProps: {
        placeholder: '请输入价值申报的分类代码',
      },
    },
    {
      fieldName: 'freight',
      component: 'InputNumber',
      label: '运费',
      componentProps: {
        placeholder: '请输入运费',
        min: 0,
        precision: 2,
      },
    },
    {
      fieldName: 'insurance',
      component: 'InputNumber',
      label: '保险费',
      componentProps: {
        placeholder: '请输入保险费',
        min: 0,
        precision: 2,
      },
    },
  ],
});

/**
 * 税务信息表单
 */
const [TaxForm, taxFormApi] = useVbenForm({
  showDefaultActions: false,
  commonConfig: {
    componentProps: {
      class: 'w-full',
      readonly: isReadonly,
    },
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  schema: [
    {
      fieldName: 'taxDueDateCode',
      component: 'Input',
      label: '确定纳税截止日期代码',
      componentProps: {
        placeholder: '请输入确定纳税截止日期代码',
      },
    },
  ],
});

const selectConfig = {
  api: '/company/getCompanyList',
  columns: [
    { headerName: '公司代码', field: 'companyCode', width: 140 },
    { headerName: '公司税号', field: 'taxCode', width: 140 },
    { headerName: '公司名称', field: 'companyName', width: 140 },
    { headerName: '邮政编码', field: 'postalCode', width: 155 },
    { headerName: '公司名称', field: 'address', width: 135 },
  ],
  title: $t('basic.pleaseSelect'),
  showSearch: true,
  immediate: false,
  searchPlaceholder: $t('basic.selectInput'),
  multiple: false,
  class: 'w-[50%] h-[70%]',
};

const selectOpen = async () => {
  try {
    selectDataRef.value.modalApi.open();
    // 监听弹窗关闭事件
    selectDataRef.value.modalApi.onClosed = () => {
      const selectedData = selectDataRef.value.modalApi.sharedData;
      if (selectedData && selectedData.length > 0) {
        const company = selectedData[0];

        // 映射API字段到表单的所有字段（根据实际API返回的字段名）
        const mappedData = {
          // 基本公司信息 - 从API获取
          importCompanyCode: company.companyCode || '',
          importTaxCode: company.taxCode || '',
          importCompanyName: company.companyName || '',
          importPostalCode: company.postalCode || '',
          importAddress: company.address || '',

          // 联系信息 - 从API获取（使用实际字段名）
          importPhone: company.phone || '',
          importContactPerson: company.contactName || '', // API字段名是contactName
          importContactPhone: company.contactPhone || '',
          importEmail: company.email || '',

          // 业务分类信息 - 从API获取（使用实际字段名）
          importTypeCode: company.typeCode || '',
          importCustomsAuthority: company.customsAuthority || '',
          importGoodsClassificationCode: company.goodsClassificationCode || '',
          importPersonOrgClassification:
            company.personOrgType || 'organization', // API字段名是personOrgType
          importDeclarationDeptCode: company.declarationDeptCode || '',
          importTransportModeCode: company.transportModeCode || '',
        };
        // 更新表单数据
        importCompanyFormApi.setValues(mappedData);
      }
    };
  } catch (error) {
    console.error('Error in selectOpen:', error);
    ElNotification({
      type: 'error',
      message: '打开选择窗口失败',
      duration: 2500,
    });
  }
};

// 出口公司选择配置
const selectExportConfig = {
  api: '/company/getCompanyList',
  columns: [
    { headerName: '公司代码', field: 'companyCode', width: 140 },
    { headerName: '公司税号', field: 'taxCode', width: 140 },
    { headerName: '公司名称', field: 'companyName', width: 140 },
    { headerName: '邮政编码', field: 'postalCode', width: 155 },
    { headerName: '公司地址', field: 'address', width: 135 },
  ],
  title: $t('basic.pleaseSelect'),
  showSearch: true,
  immediate: false,
  searchPlaceholder: $t('basic.selectInput'),
  multiple: false,
  class: 'w-[50%] h-[70%]',
};

// 出口公司选择函数
const selectExportCompanyOpen = async () => {
  try {
    selectExportDataRef.value.modalApi.open();
    // 监听弹窗关闭事件
    selectExportDataRef.value.modalApi.onClosed = () => {
      const selectedData = selectExportDataRef.value.modalApi.sharedData;
      if (selectedData && selectedData.length > 0) {
        const company = selectedData[0];

        // 映射API字段到出口公司表单的所有字段
        const mappedData = {
          // 基本公司信息
          exportCompanyCode: company.companyCode || '',
          exportTaxCode: company.taxCode || '',
          exportCompanyName: company.companyName || '',
          exportPostalCode: company.postalCode || '',
          exportAddress: company.address || '',
          exportCountryCode: company.countryCode || '',
        };

        // 更新出口公司表单数据
        exportCompanyFormApi.setValues(mappedData);
      }
    };
  } catch (error) {
    console.error('Error in selectExportCompanyOpen:', error);
    ElNotification({
      type: 'error',
      message: '打开选择窗口失败',
      duration: 2500,
    });
  }
};

// 处理卡片展开状态变化
const handleCardExpanded = (
  _index: number,
  _expanded: boolean,
  _config: any,
) => {
  // 可以在这里添加业务逻辑，比如保存展开状态等
};

// 暴露方法给父组件
defineExpose({
  formApi: {
    exportCompany: exportCompanyFormApi,
    importCompany: importCompanyFormApi,
    billOfLading: billOfLadingFormApi,
    permit: permitFormApi,
    invoice: invoiceFormApi,
    tax: taxFormApi,
  },
});
</script>

<template>
  <div class="customs-info-container" :key="formKey">
    <ExpandableCardGroup
      :cards="cardConfigs"
      container-class="customs-card-group"
      @card-expanded="handleCardExpanded"
    >
      <template #card-0>
        <ImportCompanyForm />
      </template>
      <template #card-1>
        <ExportCompanyForm />
      </template>
      <template #card-2>
        <BillOfLadingForm />
      </template>
      <template #card-3>
        <PermitForm />
      </template>
      <template #card-4>
        <InvoiceForm />
      </template>
      <template #card-5>
        <TaxForm />
      </template>
    </ExpandableCardGroup>
  </div>
  <selectData ref="selectDataRef" v-bind="selectConfig" />
  <selectData ref="selectExportDataRef" v-bind="selectExportConfig" />
</template>

<style scoped>
/* 响应式设计 */
@media (max-width: 768px) {
  .customs-info-container {
    padding: 0.5rem;
  }

  .customs-card-group {
    gap: 1rem !important;
  }
}

.customs-info-container {
  height: 100%;
  min-height: 100vh;
  padding: 1rem;
}

.customs-card-group {
  gap: 1.5rem;
}

/* 图标样式 */
:deep(.export-icon) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

:deep(.import-icon) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

:deep(.bill-icon) {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

:deep(.permit-icon) {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

:deep(.invoice-icon) {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

:deep(.tax-icon) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
