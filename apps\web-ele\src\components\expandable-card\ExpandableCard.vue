<script lang="ts" setup>
import type { ExpandableCardEmits, ExpandableCardProps } from './types';

import { computed, ref, watch } from 'vue';

import { SvgChevronDown } from '@vben/icons';

import { ElCard } from 'element-plus';

defineOptions({
  name: 'ExpandableCard',
});

const props = withDefaults(defineProps<ExpandableCardProps>(), {
  disabled: false,
  expanded: undefined,
});

const emit = defineEmits<ExpandableCardEmits>();

// 内部展开状态
const internalExpanded = ref(props.config.defaultExpanded ?? true);

// 计算实际的展开状态（支持受控和非受控模式）
const isExpanded = computed({
  get: () => props.expanded ?? internalExpanded.value,
  set: (value: boolean) => {
    if (props.expanded === undefined) {
      internalExpanded.value = value;
    }
    emit('update:expanded', value);
  },
});

// 监听 props.expanded 变化，同步内部状态
watch(
  () => props.expanded,
  (newValue) => {
    if (newValue !== undefined) {
      internalExpanded.value = newValue;
    }
  },
  { immediate: true },
);

// 切换展开状态
const toggleExpanded = () => {
  if (props.disabled) return;
  isExpanded.value = !isExpanded.value;
};

// 处理卡片点击事件
// const handleCardClick = () => {
//   emit('card-click', props.config);
// };
</script>

<template>
  <ElCard class="expandable-card" :class="[config.cardClass]">
    <template #header>
      <div
        class="card-header"
        :class="[config.headerClass, { disabled }]"
        @click="toggleExpanded"
      >
        <!-- 图标 -->
        <div v-if="config.icon" class="header-icon" :class="[config.iconClass]">
          <component :is="config.icon" />
        </div>

        <!-- 标题 -->
        <span class="header-title">{{ config.title }}</span>

        <!-- 展开/收缩图标 -->
        <div class="expand-icon" :class="[{ expanded: isExpanded, disabled }]">
          <SvgChevronDown />
        </div>
      </div>
    </template>

    <!-- 卡片内容 -->
    <div
      v-show="isExpanded"
      class="card-content"
      :class="[config.contentClass]"
    >
      <!-- 使用组件内容 -->
      <component
        v-if="config.content"
        :is="config.content"
        v-bind="config.contentProps || {}"
      />

      <!-- 使用插槽内容 -->
      <slot v-else></slot>
    </div>
  </ElCard>
</template>

<style scoped>
@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-icon {
    width: 1.75rem;
    height: 1.75rem;
  }

  .header-icon :deep(svg) {
    width: 0.875rem;
    height: 0.875rem;
  }

  .header-title {
    font-size: 0.875rem;
  }

  .expand-icon {
    width: 1.75rem;
    height: 1.75rem;
  }

  .expand-icon :deep(svg) {
    width: 1rem;
    height: 1rem;
  }
}

.expandable-card {
  border-radius: 12px;
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 10%),
    0 2px 4px -1px rgb(0 0 0 / 6%);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.expandable-card:hover {
  box-shadow:
    0 10px 15px -3px rgb(0 0 0 / 10%),
    0 4px 6px -2px rgb(0 0 0 / 5%);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  padding: 0;
  cursor: pointer;
  user-select: none;
  transition: opacity 0.3s ease;
}

.card-header.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  color: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 12%);
  transition: all 0.3s ease;
}

.header-icon :deep(svg) {
  width: 1rem;
  height: 1rem;
}

.header-title {
  flex: 1;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.expand-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 4px;
  transition:
    transform 0.3s ease,
    color 0.3s ease;
}

.expand-icon:hover:not(.disabled) {
  color: #374151;
  background-color: rgb(0 0 0 / 5%);
}

.expand-icon :deep(svg) {
  width: 1.25rem;
  height: 1.25rem;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.expand-icon.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.card-content {
  animation: slide-down 0.3s ease-out;
}

/* 表单内容样式 */
:deep(.el-card__body) {
  padding: 1.5rem;
}
</style>
