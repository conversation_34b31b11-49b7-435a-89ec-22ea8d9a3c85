/* Element Plus MessageBox 防抖动样式修复 */

/* 只针对带有 prevent-body-scroll 类的 MessageBox 进行优化 */
.el-message-box.prevent-body-scroll {
  /* 确保弹窗不会影响页面滚动条 */
  overflow: visible !important;
  /* 添加平滑过渡效果 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局防止弹窗导致的滚动条抖动 - 使用更安全的方式 */
@supports (scrollbar-gutter: stable) {
  body {
    /* 保持滚动条槽位，防止滚动条出现/消失导致的布局偏移 */
    scrollbar-gutter: stable;
  }
}

/* 只针对带有 prevent-body-scroll 类的 MessageBox 优化动画 */
.el-message-box.prevent-body-scroll.msgbox-fade-enter-active,
.el-message-box.prevent-body-scroll.msgbox-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.el-message-box.prevent-body-scroll.msgbox-fade-enter-from,
.el-message-box.prevent-body-scroll.msgbox-fade-leave-to {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.9);
}

/* 防止弹窗出现时的闪烁 - 只针对我们的自定义类 */
.el-message-box.prevent-body-scroll .el-message-box__wrapper {
  /* 使用 GPU 加速 */
  will-change: transform, opacity;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
