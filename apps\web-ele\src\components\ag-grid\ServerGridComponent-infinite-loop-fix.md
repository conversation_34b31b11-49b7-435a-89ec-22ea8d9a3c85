# ServerGridComponent 无限循环问题修复

## 🐛 问题描述

用户反馈：设置 pageSize 为 10，点击下一页时出现无限加载，一直发送 `pageIndex: 1, pageSize: 10` 的请求。

## 🔍 问题分析

经过分析，发现了以下几个导致无限循环的问题：

### 1. 响应式状态导致的循环
```javascript
// ❌ 问题代码
const currentPageSize = ref(props.pageSize);

const mergedGridOptions = computed(() => {
  return {
    ...props.gridOptions,
    paginationPageSize: currentPageSize.value, // 这里使用响应式值
    cacheBlockSize: currentPageSize.value,
    serverSideDatasource: serverSideDataSource,
  };
});
```

**问题**：当 `currentPageSize.value` 变化时，`mergedGridOptions` 会重新计算，导致 AG-Grid 重新初始化，触发新的数据请求。

### 2. 分页事件中的状态修改
```javascript
// ❌ 问题代码
const onPaginationChanged = (params: any) => {
  const pageSize = params.api.paginationGetPageSize();
  currentPageSize.value = pageSize; // 这里修改响应式状态
  emit('paginationChanged', { pageSize, pageIndex });
};
```

**问题**：修改 `currentPageSize.value` 会触发 `mergedGridOptions` 重新计算，形成循环。

### 3. 搜索方法中的页面大小处理
```javascript
// ❌ 问题代码
const search = (params: any) => {
  if (params.pageSize) {
    currentPageSize.value = Number(params.pageSize); // 修改响应式状态
  }
  handleRefresh();
};
```

**问题**：同样会触发 `mergedGridOptions` 重新计算。

## ✅ 修复方案

### 1. 移除响应式状态依赖
```javascript
// ✅ 修复后
// 删除了 currentPageSize 响应式变量
const mergedGridOptions = computed(() => {
  return {
    ...props.gridOptions,
    paginationPageSize: props.pageSize, // 直接使用 props
    cacheBlockSize: props.pageSize,
    serverSideDatasource: serverSideDataSource,
  };
});
```

### 2. 简化分页事件处理
```javascript
// ✅ 修复后
const onPaginationChanged = (params: any) => {
  const pageSize = params.api.paginationGetPageSize();
  const pageIndex = params.api.paginationGetCurrentPage() + 1;
  
  console.warn('AG-Grid 分页变化 - 页面大小:', pageSize, '页码:', pageIndex);
  
  // 只触发事件，不修改内部状态
  emit('paginationChanged', { pageSize, pageIndex });
};
```

### 3. 简化搜索方法
```javascript
// ✅ 修复后
const search = (params: any) => {
  if (isRefreshing.value) {
    console.warn('正在刷新中，跳过此次请求');
    return;
  }

  isRefreshing.value = true;

  // 只处理搜索参数，不处理页面大小
  const { pageSize: _pageSize, ...searchData } = params;
  searchParams.value = searchData;

  handleRefresh();

  setTimeout(() => {
    isRefreshing.value = false;
  }, 1000);
};
```

### 4. 使用 AG-Grid API 获取页面大小
```javascript
// ✅ 修复后
const actualPageSize = params.api
  ? params.api.paginationGetPageSize()
  : props.pageSize; // 直接使用 props 作为后备
```

### 5. 设置明确的初始页面大小
```vue
<!-- ✅ 修复后 -->
<ServerGridComponent
  ref="gridRef"
  :api-function="getMaterialList"
  :column-defs="columnDefs"
  :page-size="10"  <!-- 明确设置页面大小 -->
  :default-col-def="defaultColDef"
  :row-selection="rowSelection"
  :table-actions="[]"
  @data-loaded="handleDataLoaded"
  @load-error="handleLoadError"
  @pagination-changed="handlePaginationChanged"
/>
```

## 🎯 修复结果

1. **消除了无限循环**：不再有响应式状态导致的循环更新
2. **保持功能完整**：分页、搜索等功能正常工作
3. **性能提升**：减少了不必要的重新渲染
4. **代码简化**：移除了复杂的状态管理逻辑

## 📝 经验总结

1. **避免在 computed 中使用会变化的响应式状态**，特别是会导致组件重新初始化的配置
2. **AG-Grid 的配置应该尽量稳定**，避免频繁变化导致重新初始化
3. **使用 AG-Grid 的 API 获取状态**，而不是维护额外的响应式状态
4. **分离关注点**：页面大小由 props 控制，搜索参数由内部状态管理

## 🔧 测试建议

1. 设置不同的页面大小（10、20、50）
2. 测试分页功能（下一页、上一页、跳转页面）
3. 测试搜索功能
4. 检查网络请求是否正常（不应该有重复请求）

修复完成！🎉
