import { pagedRequestClient, requestClient } from '#/api/request';

/**
 * 查询料件基础信息（支持分页）
 * @param data 参数
 */
export async function getMaterialList(data: any) {
  return pagedRequestClient.post<any>('/bom/getMaterialList', data);
}

/**
 * 查询料件基础信息
 * @param data 参数
 */
export async function getMaterialListByFG(data: any) {
  return requestClient.post<any>('/bom/getMaterialListByFG', data);
}

/**
 * 新增技术单耗料件信息
 * @param data 参数
 */
export async function tcmAddOrEdit(data: any) {
  return requestClient.post<any>('/bom/tcmAddOrEdit', data);
}

/**
 * 查询单条技术单耗料件信息
 * @param data 参数
 */
export async function getTCMInfo(data: any) {
  return requestClient.post<any>('/bom/getTCMInfo', data);
}

/**
 * 删除技术单耗料件信息
 * @param data 参数
 */
export async function deleteTCMInfo(data: any) {
  return requestClient.post<any>('/bom/deleteTCMInfo', data);
}

/**
 * 查询成品编号对应的料件信息
 * @param data 参数
 */
export async function getTechConsumptionList(data: any) {
  return requestClient.post<any>('/bom/getTechConsumptionList', data);
}

/**
 * 查询成品编号对应的料件信息
 * @param data 参数
 */
export async function insertMcodeByFg(data: any) {
  return requestClient.post<any>('/bom/insertMcodeByFg', data);
}

/**
 * 发布料件
 * @param data 参数
 */
export async function releaseMaterial(data: any) {
  return requestClient.post<any>('/bom/releaseMaterial', data);
}

/**
 * 取消发布料件
 * @param data 参数
 */
export async function cancelReleaseMaterial(data: any) {
  return requestClient.post<any>('/bom/cancelReleaseMaterial', data);
}
