import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';

type MessageType = 'error' | 'info' | 'success' | 'warning';
type MessageBoxType = 'alert' | 'confirm' | 'prompt';

export function useMessage() {
  // ElMessage 统一调用方法
  function showMessage(type: MessageType, message: string, duration = 2500) {
    ElMessage({
      type,
      message,
      duration,
    });
  }

  // ElNotification 统一调用方法
  function showNotification(
    type: MessageType,
    message: string,
    duration = 2500,
  ) {
    ElNotification({
      type,
      message,
      duration,
    });
  }

  // ElMessageBox 统一调用方法
  function showMessageBox(
    type: MessageBoxType,
    message: string,
    title: string = 'Tips',
    boxType: 'error' | 'info' | 'success' | 'warning' = 'warning', // ElMessageBox.alert的类型
    callback?: (
      action: 'cancel' | 'confirm' | 'input' | null,
      value?: string,
    ) => void,
  ) {
    const options: any = {
      title,
      message,
      type: 'warning', // 默认警告类型
      lockScroll: false, // 禁用滚动锁定，防止抖动
      customClass: 'prevent-body-scroll', // 添加自定义类名
    };

    // 根据不同的 type 显示对话框
    switch (type) {
      case 'alert': {
        ElMessageBox.alert(message, title, { ...options, type: boxType }) // 使用传入的boxType
          .then(() => {
            if (callback) callback('confirm');
          });

        break;
      }
      case 'confirm': {
        ElMessageBox.confirm(message, title, options)
          .then(() => {
            if (callback) callback('confirm');
          })
          .catch(() => {
            if (callback) callback('cancel');
          });

        break;
      }
      case 'prompt': {
        ElMessageBox.prompt(message, title, { ...options, type: boxType })
          .then(({ value }) => {
            if (callback) callback('confirm', value);
          })
          .catch(() => {
            if (callback) callback('cancel');
          });

        break;
      }
      // No default
    }
  }

  // 新增：防抖动的确认对话框
  function showConfirm(
    message: string,
    title: string = 'Tips',
    options: any = {},
  ) {
    return ElMessageBox.confirm(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      lockScroll: false, // 禁用滚动锁定，防止抖动
      customClass: 'prevent-body-scroll', // 添加自定义类名
      ...options, // 允许覆盖默认配置
    });
  }

  return {
    showMessage,
    showNotification,
    showMessageBox, // 返回新的方法
    showConfirm, // 返回防抖动的确认对话框方法
  };
}
