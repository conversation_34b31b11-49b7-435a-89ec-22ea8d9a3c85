<script lang="ts" setup>
import type { ExpandableCardConfig } from './types';

import { ref } from 'vue';

import ExpandableCard from './ExpandableCard.vue';

interface Props {
  /** 卡片配置列表 */
  cards: ExpandableCardConfig[];
  /** 容器样式类名 */
  containerClass?: string;
  /** 卡片间距 */
  gap?: string;
  /** 是否只允许一个卡片展开 */
  accordion?: boolean;
}

defineOptions({
  name: 'ExpandableCardGroup',
});

const props = withDefaults(defineProps<Props>(), {
  containerClass: '',
  gap: '1.5rem',
  accordion: false,
});

// 存储每个卡片的展开状态
const expandedStates = ref<boolean[]>(
  props.cards.map((card) => card.defaultExpanded ?? true),
);

// 处理卡片展开状态变化
const handleCardExpanded = (index: number, expanded: boolean) => {
  // 如果是手风琴模式，关闭其他卡片
  if (props.accordion && expanded) {
    expandedStates.value = expandedStates.value.map((_, i) => i === index);
  } else {
    expandedStates.value[index] = expanded;
  }
};

// 暴露方法给父组件
const expandCard = (index: number) => {
  if (index >= 0 && index < props.cards.length) {
    handleCardExpanded(index, true);
  }
};

const collapseCard = (index: number) => {
  if (index >= 0 && index < props.cards.length) {
    handleCardExpanded(index, false);
  }
};

defineExpose({
  expandCard,
  collapseCard,
  expandedStates,
});
</script>

<template>
  <div class="expandable-card-group" :class="[containerClass]" :style="{ gap }">
    <ExpandableCard
      v-for="(card, index) in cards"
      :key="`card-${index}`"
      :config="card"
      :expanded="expandedStates[index]"
      @update:expanded="(expanded) => handleCardExpanded(index, expanded)"
    >
      <!-- 传递插槽内容 -->
      <template v-if="$slots[`card-${index}`]" #default>
        <slot :name="`card-${index}`" :config="card" :index="index"></slot>
      </template>
    </ExpandableCard>
  </div>
</template>

<style scoped>
/* 响应式设计 */
@media (max-width: 768px) {
  .expandable-card-group {
    gap: 1rem !important;
    padding: 0.5rem;
  }
}

.expandable-card-group {
  display: flex;
  flex-direction: column;
  width: 100%;
}
</style>
